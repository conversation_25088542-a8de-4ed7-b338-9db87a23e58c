{"buildFiles": ["J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "J:\\Android\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "J:\\Android\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}