import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/constants.dart';

enum ButtonVariant { primary, secondary, outlined, text, floating }

enum ButtonSize { small, medium, large }

class CustomButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool enabled;
  final bool isLoading;
  final ButtonVariant variant;
  final ButtonSize size;
  final IconData? icon;
  final IconData? suffixIcon;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool fullWidth;
  final bool enableFeedback;
  final String? tooltip;
  final double? elevation;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.enabled = true,
    this.isLoading = false,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.suffixIcon,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.fullWidth = false,
    this.enableFeedback = true,
    this.tooltip,
    this.elevation,
  });

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildButton(context),
        );
      },
    );

    if (widget.tooltip != null) {
      return Tooltip(message: widget.tooltip!, child: button);
    }

    return button;
  }

  Widget _buildButton(BuildContext context) {
    final buttonWidth = widget.fullWidth ? double.infinity : widget.width;

    switch (widget.variant) {
      case ButtonVariant.primary:
        return _buildElevatedButton(context, buttonWidth);
      case ButtonVariant.secondary:
        return _buildSecondaryButton(context, buttonWidth);
      case ButtonVariant.outlined:
        return _buildOutlinedButton(context, buttonWidth);
      case ButtonVariant.text:
        return _buildTextButton(context, buttonWidth);
      case ButtonVariant.floating:
        return _buildFloatingButton(context);
    }
  }

  void _handlePress() {
    if (widget.enableFeedback) {
      HapticFeedback.lightImpact();
    }

    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    widget.onPressed?.call();
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return AppConstants.minButtonHeight;
      case ButtonSize.large:
        return 56;
    }
  }

  EdgeInsetsGeometry _getButtonPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingLarge,
          vertical: AppConstants.paddingMedium,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingXLarge,
          vertical: AppConstants.paddingLarge,
        );
    }
  }

  double _getBorderRadius() {
    switch (widget.size) {
      case ButtonSize.small:
        return AppConstants.borderRadiusSmall;
      case ButtonSize.medium:
        return AppConstants.borderRadiusMedium;
      case ButtonSize.large:
        return AppConstants.borderRadiusLarge;
    }
  }

  double _getElevation() {
    if (widget.elevation != null) return widget.elevation!;
    return widget.enabled ? AppConstants.elevationLow : 0;
  }

  double _getFontSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 14;
      case ButtonSize.medium:
        return 16;
      case ButtonSize.large:
        return 18;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  Widget _buildElevatedButton(BuildContext context, double? width) {
    return SizedBox(
      width: width,
      height: widget.height ?? _getButtonHeight(),
      child: ElevatedButton(
        onPressed: widget.enabled && !widget.isLoading ? _handlePress : null,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              widget.backgroundColor ?? Theme.of(context).colorScheme.primary,
          foregroundColor:
              widget.textColor ?? Theme.of(context).colorScheme.onPrimary,
          padding: widget.padding ?? _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius:
                widget.borderRadius ??
                BorderRadius.circular(_getBorderRadius()),
          ),
          elevation: _getElevation(),
          shadowColor: Theme.of(
            context,
          ).colorScheme.primary.withValues(alpha: 0.3),
        ),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildSecondaryButton(BuildContext context, double? width) {
    return SizedBox(
      width: width,
      height: widget.height ?? _getButtonHeight(),
      child: ElevatedButton(
        onPressed: widget.enabled && !widget.isLoading ? _handlePress : null,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              widget.backgroundColor ?? Theme.of(context).colorScheme.secondary,
          foregroundColor:
              widget.textColor ?? Theme.of(context).colorScheme.onSecondary,
          padding: widget.padding ?? _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius:
                widget.borderRadius ??
                BorderRadius.circular(_getBorderRadius()),
          ),
          elevation: _getElevation(),
          shadowColor: Theme.of(
            context,
          ).colorScheme.secondary.withValues(alpha: 0.3),
        ),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildOutlinedButton(BuildContext context, double? width) {
    return SizedBox(
      width: width,
      height: widget.height ?? _getButtonHeight(),
      child: OutlinedButton(
        onPressed: widget.enabled && !widget.isLoading ? _handlePress : null,
        style: OutlinedButton.styleFrom(
          foregroundColor:
              widget.textColor ?? Theme.of(context).colorScheme.primary,
          side: BorderSide(
            color:
                widget.backgroundColor ?? Theme.of(context).colorScheme.primary,
            width: 1.5,
          ),
          padding: widget.padding ?? _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius:
                widget.borderRadius ??
                BorderRadius.circular(_getBorderRadius()),
          ),
        ),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildTextButton(BuildContext context, double? width) {
    return SizedBox(
      width: width,
      height: widget.height ?? _getButtonHeight(),
      child: TextButton(
        onPressed: widget.enabled && !widget.isLoading ? _handlePress : null,
        style: TextButton.styleFrom(
          foregroundColor:
              widget.textColor ?? Theme.of(context).colorScheme.primary,
          padding: widget.padding ?? _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius:
                widget.borderRadius ??
                BorderRadius.circular(_getBorderRadius()),
          ),
        ),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildFloatingButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: widget.enabled && !widget.isLoading ? _handlePress : null,
      backgroundColor:
          widget.backgroundColor ?? Theme.of(context).colorScheme.primary,
      foregroundColor:
          widget.textColor ?? Theme.of(context).colorScheme.onPrimary,
      elevation: _getElevation(),
      label: _buildButtonContent(context),
      icon: widget.icon != null
          ? Icon(widget.icon, size: _getIconSize())
          : null,
    );
  }

  Widget _buildButtonContent(BuildContext context) {
    if (widget.isLoading) {
      return SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.textColor ?? Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      );
    }

    final children = <Widget>[];

    if (widget.icon != null) {
      children.add(Icon(widget.icon, size: _getIconSize()));
      children.add(SizedBox(width: AppConstants.paddingSmall));
    }

    children.add(
      Text(
        widget.text,
        style: TextStyle(
          fontSize: _getFontSize(),
          fontWeight: FontWeight.w600,
          color: widget.textColor,
        ),
      ),
    );

    if (widget.suffixIcon != null) {
      children.add(SizedBox(width: AppConstants.paddingSmall));
      children.add(Icon(widget.suffixIcon, size: _getIconSize()));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }
}
