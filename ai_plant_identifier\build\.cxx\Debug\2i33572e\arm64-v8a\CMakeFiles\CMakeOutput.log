The target system is: Android - 1 - aarch64
The host system is: Windows - 10.0.26100 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;
Id flags: -c;--target=aarch64-none-linux-android21 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;;
Id flags: -c;--target=aarch64-none-linux-android21 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):J:\Android\cmake\3.22.1\bin\ninja.exe cmTC_d7f27 && [1/2] Building C object CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o

Android (11349228, based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)

Target: aarch64-none-linux-android21

Thread model: posix

InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin

 (in-process)

 "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe" -cc1 -triple aarch64-none-linux-android21 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -mllvm -treat-scalable-fixed-error-as-warning -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -resource-dir J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17 -dependency-file "CMakeFiles\\cmTC_d7f27.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat "-fdebug-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o -x c J:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c

clang -cc1 version 17.0.2 based upon LLVM 17.0.2 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"

ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking C executable cmTC_d7f27

Android (11349228, based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)

Target: aarch64-none-linux-android21

Thread model: posix

InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin

 "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=both --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_d7f27 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include]
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include]
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):J:\Android\cmake\3.22.1\bin\ninja.exe cmTC_d7f27 && [1/2] Building C object CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android (11349228  based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)]
  ignore line: [Target: aarch64-none-linux-android21]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin]
  ignore line: [ (in-process)]
  ignore line: [ "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe" -cc1 -triple aarch64-none-linux-android21 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -mllvm -treat-scalable-fixed-error-as-warning -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -resource-dir J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17 -dependency-file "CMakeFiles\\cmTC_d7f27.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat "-fdebug-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o -x c J:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 17.0.2 based upon LLVM 17.0.2 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_d7f27]
  ignore line: [Android (11349228  based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)]
  ignore line: [Target: aarch64-none-linux-android21]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin]
  link line: [ "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=both --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_d7f27 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld] ==> ignore
    arg [--sysroot=J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=both] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_d7f27] ==> ignore
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o] ==> obj [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--no-undefined-version] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [CMakeFiles/cmTC_d7f27.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o] ==> obj [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o]
  remove lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o]
  implicit dirs: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):J:\Android\cmake\3.22.1\bin\ninja.exe cmTC_33826 && [1/2] Building CXX object CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o

Android (11349228, based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)

Target: aarch64-none-linux-android21

Thread model: posix

InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin

 (in-process)

 "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe" -cc1 -triple aarch64-none-linux-android21 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -mllvm -treat-scalable-fixed-error-as-warning -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -resource-dir J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17 -dependency-file "CMakeFiles\\cmTC_33826.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro "-fdebug-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o -x c++ J:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp

clang -cc1 version 17.0.2 based upon LLVM 17.0.2 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"

ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking CXX executable cmTC_33826

Android (11349228, based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)

Target: aarch64-none-linux-android21

Thread model: posix

InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin

 "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=both --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_33826 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o -Bstatic -lc++ -Bdynamic -lm J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include]
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include]
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):J:\Android\cmake\3.22.1\bin\ninja.exe cmTC_33826 && [1/2] Building CXX object CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android (11349228  based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)]
  ignore line: [Target: aarch64-none-linux-android21]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin]
  ignore line: [ (in-process)]
  ignore line: [ "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe" -cc1 -triple aarch64-none-linux-android21 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu generic -target-feature +neon -target-feature +v8a -target-feature +fix-cortex-a53-835769 -target-abi aapcs -mllvm -treat-scalable-fixed-error-as-warning -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -resource-dir J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17 -dependency-file "CMakeFiles\\cmTC_33826.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include -internal-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro "-fdebug-compilation-dir=I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/arm64-v8a/CMakeFiles/CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -target-feature +outline-atomics -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o -x c++ J:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 17.0.2 based upon LLVM 17.0.2 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/include]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_33826]
  ignore line: [Android (11349228  based on r487747e) clang version 17.0.2 (https://android.googlesource.com/toolchain/llvm-project d9f89f4d16663d5012e5c09495f3b30ece3d2362)]
  ignore line: [Target: aarch64-none-linux-android21]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin]
  link line: [ "J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -EL --fix-cortex-a53-843419 -z now -z relro -z max-page-size=4096 --hash-style=both --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_33826 J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21 -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o -Bstatic -lc++ -Bdynamic -lm J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld] ==> ignore
    arg [--sysroot=J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=both] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_33826] ==> ignore
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o] ==> obj [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LJ:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--no-undefined-version] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [CMakeFiles/cmTC_33826.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-Bstatic] ==> search static
    arg [-lc++] ==> lib [c++]
    arg [-Bdynamic] ==> search dynamic
    arg [-lm] ==> lib [m]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o] ==> obj [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o]
  remove lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtbegin_dynamic.o;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21/crtend_android.o]
  implicit dirs: [J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/21;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;J:/Android/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


