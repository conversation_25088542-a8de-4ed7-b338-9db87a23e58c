import 'package:flutter/material.dart';
import '../../utils/constants.dart';

enum CardVariant { elevated, outlined, filled }
enum CardSize { small, medium, large }

class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final CardVariant variant;
  final CardSize size;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final bool enableHoverEffect;
  final bool enableRipple;
  final String? tooltip;
  final Widget? header;
  final Widget? footer;
  final List<BoxShadow>? customShadows;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.variant = CardVariant.elevated,
    this.size = CardSize.medium,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.padding,
    this.margin,
    this.borderRadius,
    this.enableHoverEffect = true,
    this.enableRipple = true,
    this.tooltip,
    this.header,
    this.footer,
    this.customShadows,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: _getBaseElevation(),
      end: _getBaseElevation() + 4,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final card = AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enableHoverEffect ? _scaleAnimation.value : 1.0,
          child: _buildCard(context),
        );
      },
    );

    if (widget.tooltip != null) {
      return Tooltip(
        message: widget.tooltip!,
        child: card,
      );
    }

    return card;
  }

  Widget _buildCard(BuildContext context) {
    final cardContent = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (widget.header != null) ...[
          widget.header!,
          if (_getContentPadding().vertical > 0)
            SizedBox(height: _getContentPadding().vertical / 2),
        ],
        Flexible(child: widget.child),
        if (widget.footer != null) ...[
          if (_getContentPadding().vertical > 0)
            SizedBox(height: _getContentPadding().vertical / 2),
          widget.footer!,
        ],
      ],
    );

    switch (widget.variant) {
      case CardVariant.elevated:
        return _buildElevatedCard(context, cardContent);
      case CardVariant.outlined:
        return _buildOutlinedCard(context, cardContent);
      case CardVariant.filled:
        return _buildFilledCard(context, cardContent);
    }
  }

  Widget _buildElevatedCard(BuildContext context, Widget content) {
    return Container(
      margin: widget.margin ?? _getCardMargin(),
      child: Material(
        color: widget.backgroundColor ?? Theme.of(context).cardColor,
        elevation: widget.elevation ?? 
            (widget.enableHoverEffect ? _elevationAnimation.value : _getBaseElevation()),
        borderRadius: widget.borderRadius ?? _getBorderRadius(),
        shadowColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        child: _buildInkWell(context, content),
      ),
    );
  }

  Widget _buildOutlinedCard(BuildContext context, Widget content) {
    return Container(
      margin: widget.margin ?? _getCardMargin(),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).cardColor,
        border: Border.all(
          color: widget.borderColor ?? 
              Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        borderRadius: widget.borderRadius ?? _getBorderRadius(),
        boxShadow: widget.customShadows ?? [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildInkWell(context, content),
    );
  }

  Widget _buildFilledCard(BuildContext context, Widget content) {
    return Container(
      margin: widget.margin ?? _getCardMargin(),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? 
            Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: widget.borderRadius ?? _getBorderRadius(),
        boxShadow: widget.customShadows ?? [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildInkWell(context, content),
    );
  }

  Widget _buildInkWell(BuildContext context, Widget content) {
    if (widget.onTap == null && widget.onLongPress == null) {
      return Padding(
        padding: widget.padding ?? _getContentPadding(),
        child: content,
      );
    }

    return InkWell(
      onTap: widget.onTap,
      onLongPress: widget.onLongPress,
      onHover: widget.enableHoverEffect ? _handleHover : null,
      borderRadius: widget.borderRadius ?? _getBorderRadius(),
      splashColor: widget.enableRipple 
          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
          : Colors.transparent,
      highlightColor: widget.enableRipple
          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.05)
          : Colors.transparent,
      child: Padding(
        padding: widget.padding ?? _getContentPadding(),
        child: content,
      ),
    );
  }

  void _handleHover(bool isHovered) {
    if (_isHovered != isHovered) {
      _isHovered = isHovered;
      if (isHovered) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  double _getBaseElevation() {
    if (widget.elevation != null) return widget.elevation!;
    switch (widget.size) {
      case CardSize.small:
        return AppConstants.elevationLow;
      case CardSize.medium:
        return AppConstants.elevationMedium;
      case CardSize.large:
        return AppConstants.elevationHigh;
    }
  }

  EdgeInsetsGeometry _getContentPadding() {
    if (widget.padding != null) return widget.padding!;
    switch (widget.size) {
      case CardSize.small:
        return const EdgeInsets.all(AppConstants.paddingSmall);
      case CardSize.medium:
        return const EdgeInsets.all(AppConstants.paddingMedium);
      case CardSize.large:
        return const EdgeInsets.all(AppConstants.paddingLarge);
    }
  }

  EdgeInsetsGeometry _getCardMargin() {
    switch (widget.size) {
      case CardSize.small:
        return const EdgeInsets.all(AppConstants.paddingXSmall);
      case CardSize.medium:
        return const EdgeInsets.all(AppConstants.paddingSmall);
      case CardSize.large:
        return const EdgeInsets.all(AppConstants.paddingMedium);
    }
  }

  BorderRadius _getBorderRadius() {
    switch (widget.size) {
      case CardSize.small:
        return BorderRadius.circular(AppConstants.borderRadiusSmall);
      case CardSize.medium:
        return BorderRadius.circular(AppConstants.borderRadiusMedium);
      case CardSize.large:
        return BorderRadius.circular(AppConstants.borderRadiusLarge);
    }
  }
}

// Specialized card components
class PlantCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? imageUrl;
  final Widget? image;
  final VoidCallback? onTap;
  final List<Widget>? actions;
  final Widget? trailing;

  const PlantCard({
    super.key,
    required this.title,
    this.subtitle,
    this.imageUrl,
    this.image,
    this.onTap,
    this.actions,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (image != null || imageUrl != null) ...[
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(AppConstants.borderRadiusMedium),
              ),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: image ?? 
                    Image.network(
                      imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest,
                          child: Icon(
                            Icons.local_florist,
                            size: 48,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        );
                      },
                    ),
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
          ],
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: AppConstants.paddingXSmall),
                      Text(
                        subtitle!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              if (trailing != null) ...[
                const SizedBox(width: AppConstants.paddingMedium),
                trailing!,
              ],
            ],
          ),
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }
}
