{"logs": [{"outputFile": "com.example.ai_plant_identifier.app-mergeDebugResources-39:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5de7b6ff8bfa08c38ace6a851d79c347\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2788,2882,2985,3082,3184,3286,3384,3967", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "2877,2980,3077,3179,3281,3379,3501,4063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\90753583574ec01745115bf8fdf46bbb\\transformed\\appcompat-1.1.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,886,977,1069,1164,1258,1359,1452,1547,1641,1732,1823,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,77,90,91,94,93,100,92,94,93,90,90,78,105,100,96,108,99,109,159,102,79", "endOffsets": "207,311,418,505,605,725,803,881,972,1064,1159,1253,1354,1447,1542,1636,1727,1818,1897,2003,2104,2201,2310,2410,2520,2680,2783,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,886,977,1069,1164,1258,1359,1452,1547,1641,1732,1823,1902,2008,2109,2206,2315,2415,2525,2685,3887", "endColumns": "106,103,106,86,99,119,77,77,90,91,94,93,100,92,94,93,90,90,78,105,100,96,108,99,109,159,102,79", "endOffsets": "207,311,418,505,605,725,803,881,972,1064,1159,1253,1354,1447,1542,1636,1727,1818,1897,2003,2104,2201,2310,2410,2520,2680,2783,3962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1cd8570e01f5b29ce5b2664076fb0162\\transformed\\preference-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,263,344,486,655,739", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "172,258,339,481,650,734,816"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3506,3578,3664,3745,4068,4237,4321", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "3573,3659,3740,3882,4232,4316,4398"}}]}]}