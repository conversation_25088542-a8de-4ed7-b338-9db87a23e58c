# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = I$:/ALL$ THE$ PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/armeabi-v7a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\.cxx\Debug\2i33572e\armeabi-v7a" && J:\Android\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\.cxx\Debug\2i33572e\armeabi-v7a" && J:\Android\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SJ:\flutter\src\flutter\packages\flutter_tools\gradle\src\main\scripts -B"I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\.cxx\Debug\2i33572e\armeabi-v7a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: I:/ALL THE PROJECTS/Neuroxes/PlantApp/ai_plant_identifier/build/.cxx/Debug/2i33572e/armeabi-v7a

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake J$:/Android/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake J$:/Android/ndk/26.3.11579264/build/cmake/android.toolchain.cmake J$:/Android/ndk/26.3.11579264/build/cmake/flags.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake J$:/Android/ndk/26.3.11579264/build/cmake/platforms.cmake J$:/flutter/src/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake J$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake J$:/Android/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake J$:/Android/ndk/26.3.11579264/build/cmake/android.toolchain.cmake J$:/Android/ndk/26.3.11579264/build/cmake/flags.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake J$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake J$:/Android/ndk/26.3.11579264/build/cmake/platforms.cmake J$:/flutter/src/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
