{"buildFiles": ["J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86_64", "clean"]], "buildTargetsCommandComponents": ["J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}