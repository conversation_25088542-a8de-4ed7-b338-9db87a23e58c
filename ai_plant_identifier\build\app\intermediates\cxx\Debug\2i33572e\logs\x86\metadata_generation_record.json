[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86\\android_gradle_build.json due to:", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86'", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86'", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"J:\\\\Android\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HJ:\\\\flutter\\\\src\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=J:\\\\Android\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_ANDROID_NDK=J:\\\\Android\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=J:\\\\Android\\\\ndk\\\\26.3.11579264\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=J:\\\\Android\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=I:\\\\ALL THE PROJECTS\\\\Neuroxes\\\\PlantApp\\\\ai_plant_identifier\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2i33572e\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=I:\\\\ALL THE PROJECTS\\\\Neuroxes\\\\PlantApp\\\\ai_plant_identifier\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2i33572e\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BI:\\\\ALL THE PROJECTS\\\\Neuroxes\\\\PlantApp\\\\ai_plant_identifier\\\\build\\\\.cxx\\\\Debug\\\\2i33572e\\\\x86\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"J:\\\\Android\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HJ:\\\\flutter\\\\src\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=J:\\\\Android\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_ANDROID_NDK=J:\\\\Android\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=J:\\\\Android\\\\ndk\\\\26.3.11579264\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=J:\\\\Android\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=I:\\\\ALL THE PROJECTS\\\\Neuroxes\\\\PlantApp\\\\ai_plant_identifier\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2i33572e\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=I:\\\\ALL THE PROJECTS\\\\Neuroxes\\\\PlantApp\\\\ai_plant_identifier\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2i33572e\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BI:\\\\ALL THE PROJECTS\\\\Neuroxes\\\\PlantApp\\\\ai_plant_identifier\\\\build\\\\.cxx\\\\Debug\\\\2i33572e\\\\x86\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86\\compile_commands.json.bin existed but not I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\x86\\compile_commands.json", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "J:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]