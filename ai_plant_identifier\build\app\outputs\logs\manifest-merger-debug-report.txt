-- Merging decision tree log ---
application
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6dd1fbf413c39190d8d29b89db7f701d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6dd1fbf413c39190d8d29b89db7f701d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml
manifest
ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:shared_preferences_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1cd8570e01f5b29ce5b2664076fb0162\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90753583574ec01745115bf8fdf46bbb\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6a741d39f578b7be1dc75977ef9a9cf\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\031973263b86b451b732f3a078a92e54\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d2df11171657a406a3527f6f384c2d43\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a59b195353e6d26cd0cc72a84425688a\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\55616d6f36f3226620056341e92ac034\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ba6e81ee2733676daa0cc4180db53e5\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5efb6d3b89c37e26f9368094f6c147ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24431a4559b1fcf29d9aaeda777bc58\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ede9584b4dbec898a5a3f5eb406af9e3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\26d1bc8bea38c1899814b5601e340b38\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3203ffe012b2931c4878e70d6ff5830\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b1f04305669a229994b53a1b182d890b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64e65ce9ea201d4b0448321cd55ba9b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\177659c33aa79d4845b4efc88054df78\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0b34bed41f66a9825b82f293cfcd1799\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b864cf9296013c7a8c77d4c444567647\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e398829e668a36ef850b17608b7cca68\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d2df8b65e0796d6a90ed6c31f0c52ed\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4529353d5445a18c65913aef78eaa4ce\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9f68bc6093e7c78104620bbea2b22b3a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8ee9c40b1b431b7414e0b906c9061b8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27cf5930e6a91ac8bc35d95aa4d7e7ad\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\92b91c334b8737964e2dfa4ef6a860a8\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1147c8ce5c5932bebb1ea08c838fdf10\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203036aad901befcfff762c40de5b459\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bbc7ee1b56618fce5ba27aede02a0140\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d11e84799974cbe9f3ab7fee7054dd19\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d59ba6872da7bcbc0b6a480f0824922\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b7152c4fddeb423c2c3038d0d48b0036\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e16e95ff7715b0f597753e86cd4090c5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d46dc26fd286e8e357074050a5fd5007\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7bd6e8baf7db138ecb79c1fdf5027227\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\049015e389c724966569cf06f8c85bad\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd5d4c07f9795dd3e05cb2abd51743ac\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6dd1fbf413c39190d8d29b89db7f701d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\859cabcda5ee22dc5627d4247e812cc7\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\807811e3fdbf6a58a9b39b2417a35ecc\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2dc3d2a8725e75ac1dfc1799f78393d7\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\89b9485556e8ed3675501b84dffd6b50\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a35371b12f71919616f7dff306399091\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c53449004ee98d44149982cc82004a47\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5265f2dc2506b038563544860040498\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17bbd267b10eb7460ce458a19e197ee5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a93b8b71be6c9738fe22c71dc92bb3e2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f8f3ea88883f95ab6c8e66f69f18afe\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a55df08ed689ae038a509b9a459ffe7c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
MERGED from [:shared_preferences_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1cd8570e01f5b29ce5b2664076fb0162\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1cd8570e01f5b29ce5b2664076fb0162\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90753583574ec01745115bf8fdf46bbb\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90753583574ec01745115bf8fdf46bbb\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6a741d39f578b7be1dc75977ef9a9cf\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6a741d39f578b7be1dc75977ef9a9cf\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\031973263b86b451b732f3a078a92e54\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\031973263b86b451b732f3a078a92e54\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d2df11171657a406a3527f6f384c2d43\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d2df11171657a406a3527f6f384c2d43\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a59b195353e6d26cd0cc72a84425688a\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a59b195353e6d26cd0cc72a84425688a\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\55616d6f36f3226620056341e92ac034\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\55616d6f36f3226620056341e92ac034\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ba6e81ee2733676daa0cc4180db53e5\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ba6e81ee2733676daa0cc4180db53e5\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5efb6d3b89c37e26f9368094f6c147ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5efb6d3b89c37e26f9368094f6c147ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24431a4559b1fcf29d9aaeda777bc58\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24431a4559b1fcf29d9aaeda777bc58\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ede9584b4dbec898a5a3f5eb406af9e3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ede9584b4dbec898a5a3f5eb406af9e3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\26d1bc8bea38c1899814b5601e340b38\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\26d1bc8bea38c1899814b5601e340b38\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3203ffe012b2931c4878e70d6ff5830\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3203ffe012b2931c4878e70d6ff5830\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b1f04305669a229994b53a1b182d890b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b1f04305669a229994b53a1b182d890b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64e65ce9ea201d4b0448321cd55ba9b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64e65ce9ea201d4b0448321cd55ba9b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\177659c33aa79d4845b4efc88054df78\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\177659c33aa79d4845b4efc88054df78\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0b34bed41f66a9825b82f293cfcd1799\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0b34bed41f66a9825b82f293cfcd1799\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b864cf9296013c7a8c77d4c444567647\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b864cf9296013c7a8c77d4c444567647\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e398829e668a36ef850b17608b7cca68\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e398829e668a36ef850b17608b7cca68\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d2df8b65e0796d6a90ed6c31f0c52ed\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d2df8b65e0796d6a90ed6c31f0c52ed\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4529353d5445a18c65913aef78eaa4ce\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4529353d5445a18c65913aef78eaa4ce\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9f68bc6093e7c78104620bbea2b22b3a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9f68bc6093e7c78104620bbea2b22b3a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8ee9c40b1b431b7414e0b906c9061b8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8ee9c40b1b431b7414e0b906c9061b8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27cf5930e6a91ac8bc35d95aa4d7e7ad\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27cf5930e6a91ac8bc35d95aa4d7e7ad\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\92b91c334b8737964e2dfa4ef6a860a8\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\92b91c334b8737964e2dfa4ef6a860a8\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1147c8ce5c5932bebb1ea08c838fdf10\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1147c8ce5c5932bebb1ea08c838fdf10\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203036aad901befcfff762c40de5b459\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203036aad901befcfff762c40de5b459\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bbc7ee1b56618fce5ba27aede02a0140\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bbc7ee1b56618fce5ba27aede02a0140\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d11e84799974cbe9f3ab7fee7054dd19\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d11e84799974cbe9f3ab7fee7054dd19\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d59ba6872da7bcbc0b6a480f0824922\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3d59ba6872da7bcbc0b6a480f0824922\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b7152c4fddeb423c2c3038d0d48b0036\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b7152c4fddeb423c2c3038d0d48b0036\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e16e95ff7715b0f597753e86cd4090c5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e16e95ff7715b0f597753e86cd4090c5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d46dc26fd286e8e357074050a5fd5007\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d46dc26fd286e8e357074050a5fd5007\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7bd6e8baf7db138ecb79c1fdf5027227\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7bd6e8baf7db138ecb79c1fdf5027227\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\049015e389c724966569cf06f8c85bad\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\049015e389c724966569cf06f8c85bad\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd5d4c07f9795dd3e05cb2abd51743ac\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd5d4c07f9795dd3e05cb2abd51743ac\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6dd1fbf413c39190d8d29b89db7f701d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6dd1fbf413c39190d8d29b89db7f701d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\859cabcda5ee22dc5627d4247e812cc7\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\859cabcda5ee22dc5627d4247e812cc7\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\807811e3fdbf6a58a9b39b2417a35ecc\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\807811e3fdbf6a58a9b39b2417a35ecc\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2dc3d2a8725e75ac1dfc1799f78393d7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2dc3d2a8725e75ac1dfc1799f78393d7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\89b9485556e8ed3675501b84dffd6b50\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\89b9485556e8ed3675501b84dffd6b50\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a35371b12f71919616f7dff306399091\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a35371b12f71919616f7dff306399091\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c53449004ee98d44149982cc82004a47\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c53449004ee98d44149982cc82004a47\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5265f2dc2506b038563544860040498\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b5265f2dc2506b038563544860040498\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17bbd267b10eb7460ce458a19e197ee5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17bbd267b10eb7460ce458a19e197ee5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a93b8b71be6c9738fe22c71dc92bb3e2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a93b8b71be6c9738fe22c71dc92bb3e2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f8f3ea88883f95ab6c8e66f69f18afe\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f8f3ea88883f95ab6c8e66f69f18afe\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a55df08ed689ae038a509b9a459ffe7c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a55df08ed689ae038a509b9a459ffe7c\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\android\app\src\debug\AndroidManifest.xml
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] I:\ALL THE PROJECTS\Neuroxes\PlantApp\ai_plant_identifier\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\972da5a302e13f5bd6cba8861f3e530a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdb85e7aeedf584971405628d0dab091\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\07e4697676aa7e4e4a86d38349201a08\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.ai_plant_identifier.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.ai_plant_identifier.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5de7b6ff8bfa08c38ace6a851d79c347\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90b01435fb3287a1e897eb36ebdfdf43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
