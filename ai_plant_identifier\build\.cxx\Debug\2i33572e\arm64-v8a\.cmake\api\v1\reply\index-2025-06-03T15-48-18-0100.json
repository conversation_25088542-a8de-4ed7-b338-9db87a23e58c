{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "J:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "J:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "J:/Android/cmake/3.22.1/bin/ctest.exe", "root": "J:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-bf9757bbd0c11f536137.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-a493251a0d5db49ef767.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-4f15f01b2b52875b3e42.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-a493251a0d5db49ef767.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-4f15f01b2b52875b3e42.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-bf9757bbd0c11f536137.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}