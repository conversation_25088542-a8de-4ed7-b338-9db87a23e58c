@echo off
"J:\\Android\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HJ:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=21" ^
  "-DANDROID_PLATFORM=android-21" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=J:\\Android\\ndk\\26.3.11579264" ^
  "-DCMAKE_ANDROID_NDK=J:\\Android\\ndk\\26.3.11579264" ^
  "-DCMAKE_TOOLCHAIN_FILE=J:\\Android\\ndk\\26.3.11579264\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=J:\\Android\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\app\\intermediates\\cxx\\Debug\\2i33572e\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=I:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\app\\intermediates\\cxx\\Debug\\2i33572e\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-BI:\\ALL THE PROJECTS\\Neuroxes\\PlantApp\\ai_plant_identifier\\build\\.cxx\\Debug\\2i33572e\\armeabi-v7a" ^
  -GNinja ^
  -Wno-dev ^
  --no-warn-unused-cli
